const { sign } = require('crypto');
const fs = require('fs');

class LGDGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }

  async fetchWithTimeout(resource, options = {}, timeout = 8000) {
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(resource, {
        ...options,
        signal: controller.signal  // Pass the signal to fetch
      });
      clearTimeout(id); // Clear the timeout if the request completes in time
      return response;
    } catch (error) {
      clearTimeout(id); // Also clear timeout on other fetch errors
      if (error.name === 'AbortError') {
        // This error is specifically from controller.abort()
        throw new Error('Request timed out');
      }
      throw error; // Re-throw other errors (e.g., network errors)
    }
  }

  async generateResponse(model, prompt, config) {
    try {
      const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
      const queryParams = `key=${process.env.GEMINI_API_KEY}`;
      const fetchUrl = `${requestUrl}?${queryParams}`

      const requestBody = {
        system_instruction: { parts: [{ text: config.systemInstruction }] },
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: {
          temperature: config.temperature,
          responseMimeType: config.responseMimeType
        }
      }

      const fetchOptions = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
        sign
      }

      const response = await fetch(fetchUrl, fetchOptions);
      if (!response.ok) { throw new Error(`HTTP error! status: ${response.status}`); }

      const data = await response.json();
      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response from Gemini');
    }
  }

  async runLGDPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies } = input;

      // Step 1: Run LGD analysis prompt with transcript and competencies
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{lgd_competencies}}',
        competencies
      )

      const analysisUserPrompt = `
        Here is the transcripts:
        ${transcript}
      `

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log('Running LGD analysis prompt...');
      // console.log("UserPrompt:");
      // console.log(analysisUserPrompt);
      // console.log("Config:");
      // console.log(analysisConfig);
      // write user prompt to tmp/user_prompt.txt
      // and config to tmp/config.txt
      fs.writeFileSync('tmp/user_prompt.txt', analysisUserPrompt);
      fs.writeFileSync('tmp/config.txt', JSON.stringify(analysisConfig));
      const analysisOutput = await this.generateResponse(
        'gemini-2.5-pro-preview-05-06',
        analysisUserPrompt,
        analysisConfig
      );

      // Step 2: Run formatting prompt with analysis output
      const formattingUserPrompt = `
        You need to format this text content to JSON format
        ${analysisOutput}
      `

      const formattingConfig = {
        temperature: 0,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      console.log('Running LGD formatting prompt...');
      const finalOutput = await this.generateResponse(
        'gemini-2.5-flash-preview-05-20',
        formattingUserPrompt,
        formattingConfig
      );

      return {
        step1: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput
        },
        step2: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: finalOutput
        },
        finalOutput
      };
    } catch (error) {
      console.error('Error in LGD prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new LGDGeminiService();
